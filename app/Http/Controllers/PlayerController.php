<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use App\Models\Coupon;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Forum;
use App\Models\Lesson;
use App\Models\User;
use App\Models\Watch_history;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class PlayerController extends Controller
{

    public function course_player(Request $request, $slug, $id = '')
    {

        $course = Course::where('slug', $slug)->first();

        // Check if course exists
        if (!$course) {

            abort(404);
        }

        $enroll_status = enroll_status($course->id, auth()->user()->id);

        // check if course is paid
        if ($course->is_paid && auth()->user()->role != 'admin') {
            if (auth()->user()->role == 'student') { // for student check enrollment status
                $enroll_status = enroll_status($course->id, auth()->user()->id);
                if ($enroll_status == 'expired') {
                    Session::flash('error', 'Quyền truy cập khóa học đã hết hạn. Bạn cần mua lại để tiếp tục học');
                    return redirect()->route('course.details', ['slug' => $slug]);
                } elseif (!$enroll_status && !$course->try_learning) {
                    // quyen anh
                    Session::flash('error', 'Bạn chưa đăng ký khóa học này');
                    return redirect()->route('course.details', ['slug' => $slug]);
                }
            } elseif (auth()->user()->role == 'instructor') { // for instructor check who is course instructor
                if ($course->user_id != auth()->user()->id) {
                    Session::flash('error', 'Bạn không phải là giảng viên của khóa học này');
                    return redirect()->route('my.courses');
                }
            }
        }

        $payment_gateway = DB::table('payment_gateways')->where('identifier', 'sepay')->first();
        if ($payment_gateway) {
            $page_data['payment_gateway'] = json_decode(json_encode($payment_gateway), true);

            // Parse the keys JSON and add individual values to $page_data
            if (isset($page_data['payment_gateway']['keys'])) {
                $keys = json_decode($page_data['payment_gateway']['keys'], true);

                if ($keys) {
                    $page_data['account_name'] = $keys['name'] ?? null;
                    $page_data['bank_code'] = $keys['bank'] ?? null;
                    $page_data['account_number'] = $keys['account_number'] ?? null;
                }
            }
        }
        $check_lesson_history = Watch_history::where('course_id', $course->id)
            ->where('student_id', auth()->user()->id)->first();
        $first_lesson_of_course = Lesson::where('course_id', $course->id)
            ->orderBy('section_id', 'asc')
            ->orderBy('sort', 'asc')
            ->value('id');

        if ($id == '') {
            $id = $check_lesson_history->watching_lesson_id ?? $first_lesson_of_course;
            $lesson_exists = Lesson::where('id', $id)
                ->where('course_id', $course->id)
                ->exists();
            if(!$lesson_exists ){
                $id = $first_lesson_of_course;
            }
        } else {

            // Check if lesson ID exists in this course
            $lesson_exists = Lesson::where('id', $id)
                ->where('course_id', $course->id)
                ->exists();

            if (!$lesson_exists) {
                // If lesson not found, get the first lesson of the course
                $id = $first_lesson_of_course;
            }
        }
        // dd( $id);
        // if user has any watched history or not
        if (!$check_lesson_history && $id > 0) {
            $data = [
                'course_id' => $course->id,
                'student_id' => auth()->user()->id,
                'watching_lesson_id' => $id,
                'completed_lesson' => json_encode([]),

            ];
            Watch_history::insert($data);
        }

        // when user plays a lesson, update that lesson id as watch history
        if ($id > 0) {
            Watch_history::where('course_id', $course->id)
                ->where('student_id', auth()->user()->id)
                ->update(['watching_lesson_id' => $id]);

            // Auto mark lesson as completed for drip content progression
            $this->autoMarkLessonCompleted($course->id, $id, auth()->user()->id);
        }


        $page_data['course_details'] = $course;
        $page_data['lesson_details'] = Lesson::where('id', $id)->first();

        // Check if lesson details exists
        if (!$page_data['lesson_details']) {
            abort(404);
        }

        $page_data['history'] = Watch_history::where('course_id', $course->id)->where('student_id', auth()->user()->id)->first();
        $page_data['enroll_status'] = $enroll_status;
        $page_data['next_free_lesson_id'] = $this->next_free_lesson($course->id, $id);

        // Increment view count for the lesson
        if ($id > 0) {
            $this->incrementLessonViews($id);
            $page_data['lesson_details']['formatted_views'] = $this->formatViewCount($page_data['lesson_details']->views);
        }

        $forum_query = Forum::join('users', 'forums.user_id', 'users.id')
            ->select('forums.*', 'users.name as user_name', 'users.photo as user_photo')
            ->latest('forums.id')
            ->where('forums.parent_id', 0)
            ->where('forums.course_id', $course->id);

        if (isset($_GET['search'])) {
            $forum_query->where(function ($query) use ($request) {
                $query->where('forums.title', 'like', '%' . $request->search . '%')->where('forums.description', 'like', '%' . $request->search . '%');
            });
        }
        $page_data['questions'] = $forum_query->get();
        $this->processCouponInfo($request, $course,$page_data);
        return view('course_player.index', $page_data);
    }

    private function processCouponInfo(Request $request, $course_details, &$page_data)
    {
        // Initialize coupon variable
        $coupon = null;
        $discount = 0;

        $affiliate_ref_id = $this->getAffiliaterRefId();
        // Check for manual coupon entry
        if ($request->has('coupon') && !empty($request->get('coupon'))) {
            $code = $request->query('coupon');
            Session::flash('show_payment_modal', true);
            $coupon = Coupon::where('code', $code)
                ->whereIn("status", [1, 2])
                ->where(function ($query) {
                    $query->where("quantity", ">", 0)
                        ->orWhereNull("quantity");
                })
                ->where("expiry", ">", time())
                ->where(function ($query) use ($course_details) {
                    $query->where("course_id", $course_details->id)
                        ->orWhereNull("course_id");
                })
                ->first();

            if (!$coupon) {
                $page_data['message_coupon'] = "Mã coupon không tồn tại";
            } else {
                if ($coupon->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                    if ($affiliate_ref_id != $coupon->user_aff_id) {
                        $page_data['message_coupon'] = "Mã coupon không hợp lệ";
                    } else {
                        $discount = $coupon->discount;
                        $page_data['coupon_affiliate'] = $coupon;
                    }
                } else {
                    $discount = $coupon->discount;
                    $page_data['coupon_affiliate'] = $coupon;
                }
            }
        } else {
            // Check for affiliate coupon
            $coupon = Coupon::whereNotNull('user_aff_id')
                ->whereIn("status", [1, 2])
                ->where(function ($query) {
                    $query->where("quantity", ">", 0)
                        ->orWhereNull("quantity");
                })
                ->where("expiry", ">", time())
                ->where(function ($query) use ($course_details) {
                    $query->where("course_id", $course_details->id)
                        ->orWhereNull("course_id");
                })
                ->first();

            if ($coupon) {
                if ($coupon->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                    if ($affiliate_ref_id != $coupon->user_aff_id) {
                        $page_data['message_coupon'] = "Mã coupon không hợp lệ";
                    } else {
                        $discount = $coupon->discount;
                        $page_data['coupon_affiliate'] = $coupon;
                    }
                } else {
                    $discount = $coupon->discount;
                    $page_data['coupon_affiliate'] = $coupon;
                }
            }
        }

        // Get all valid coupons for this course
        $coupons = Coupon::where(function ($query) use ($course_details) {
            $query->where('course_id', $course_details->id)
                ->orWhereNull('course_id');
        })
            ->where("expiry", ">", time())
            ->whereIn('status', [1, 2])
            ->where(function ($query) {
                $query->where("quantity", ">", 0)
                    ->orWhereNull("quantity");
            })
            ->get();
        $arr = [];
        foreach ($coupons as $cou) {
            if ($cou->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                if ($affiliate_ref_id == $coupon->user_aff_id) {
                    $arr[] = $cou->code;
                }
            } else {
                $arr[] = $cou->code;
            }
        }


        $page_data['discount'] = $discount;
        $page_data['discount_coupon'] = $course_details->discounted_price * ($discount / 100);
        $page_data['coupons'] = $arr;
    }

    private function getAffiliaterRefId()
    {
        if (!Cookie::has("affiliate_ref")) {
            return null;
        }
        $affiliate_ref = Cookie::get("affiliate_ref");
        $affiliate_ref = hex2bin($affiliate_ref);
        return str_replace("KH-", "", $affiliate_ref);
    }

    public function set_watch_history(Request $request)
    {
        $course = Course::where('id', $request->course_id)->first();
        $enrollment = Enrollment::where('course_id', $course->id)->where('user_id', auth()->user()->id)->first();
        if (!$enrollment && (auth()->user()->role != 'admin' || !is_course_instructor($course->id))) {
            Session::flash('error', 'Bạn chưa đăng ký khóa học này');
            return redirect()->back();
        }

        $data['course_id'] = $request->course_id;
        $data['student_id'] = auth()->user()->id;

        $total_lesson = Lesson::where('course_id', $request->course_id)->pluck('id')->toArray();

        $watch_history = Watch_history::where('course_id', $request->course_id)
            ->where('student_id', auth()->user()->id)->first();

        if (isset($watch_history)) {
            $lessons = (array)json_decode($watch_history->completed_lesson);
            if (!in_array($request->lesson_id, $lessons)) {
                array_push($lessons, $request->lesson_id);
            } else {
                while (($key = array_search($request->lesson_id, $lessons)) !== false) {
                    unset($lessons[$key]);
                }
            }

            $data['completed_lesson'] = json_encode($lessons);
            $data['watching_lesson_id'] = $request->lesson_id;
            $data['completed_date'] = (count($total_lesson) == count($lessons)) ? time() : null;
            Watch_history::where('course_id', $request->course_id)->where('student_id', auth()->user()->id)->update($data);
        } else {
            $lessons = [$request->lesson_id];
            $data['completed_lesson'] = json_encode($lessons);
            $data['watching_lesson_id'] = $request->lesson_id;
            $data['completed_date'] = (count($total_lesson) == count($lessons)) ? time() : null;
            Watch_history::insert($data);
        }

        if (progress_bar($request->course_id) >= 100) {
            $certificate = Certificate::where('user_id', auth()->user()->id)->where('course_id', $request->course_id);

            if ($certificate->count() == 0) {
                $certificate_data['user_id'] = auth()->user()->id;
                $certificate_data['course_id'] = $request->course_id;
                $certificate_data['identifier'] = random(12);
                $certificate_data['created_at'] = date('Y-m-d H:i:s');
                Certificate::insert($certificate_data);
            }
        }

        return redirect()->back();
    }

    public function prepend_watermark()
    {
        return view('course_player.watermark');
    }

    public function next_free_lesson($course_id, $current_lesson_id)
    {
        // Get all lessons from the course that have valid sections, ordered by section_id and sort
        $lessons = Lesson::join('sections', 'lessons.section_id', '=', 'sections.id')
            ->where('lessons.course_id', $course_id)
            ->where('sections.course_id', $course_id)
            ->select('lessons.*')
            ->orderBy('lessons.section_id', 'asc')
            ->orderBy('lessons.sort', 'asc')
            ->get();

        $found_current = false;

        foreach ($lessons as $lesson) {
            // Start looking for next lesson after we find current lesson
            if ($found_current) {
                // Return first free lesson we find (paid_lesson = 0 means free)
                if (!$lesson->paid_lesson || $lesson->paid_lesson == 0) {
                    return $lesson->id;
                }
            }

            // Mark when we find current lesson
            if ($lesson->id == $current_lesson_id) {
                $found_current = true;
            }
        }

        // If no next free lesson found after current lesson,
        // look for the first free lesson in the entire course
        foreach ($lessons as $lesson) {
            if (!$lesson->paid_lesson || $lesson->paid_lesson == 0) {
                return $lesson->id;
            }
        }

        // If no free lesson found at all, return null
        return null;
    }

    /**
     * Increment the view count for a lesson
     *
     * @param int $lesson_id
     * @return void
     */
    private function incrementLessonViews($lesson_id)
    {
        $lesson = Lesson::find($lesson_id);
        if ($lesson) {
            $lesson->increment('views');
        }
    }

    /**
     * Format view count to be user-friendly
     *
     * @param int $views
     * @return string
     */
    private function formatViewCount($views)
    {
        if ($views >= 1000000) {
            return round($views / 1000000, 1) . 'M';
        } elseif ($views >= 1000) {
            return round($views / 1000, 1) . 'K';
        } else {
            return $views;
        }
    }
}
